<?php
session_start();

// Check if the user is logged in
if (!isset($_SESSION["user"])) {
    header("Location: login.php");
    exit();
}

// Database connection
require_once "database.php";

// Get user details
$user_id = $_SESSION["user"];
$query = "SELECT * FROM users WHERE user_id = ?";
$stmt = mysqli_prepare($conn, $query);
mysqli_stmt_bind_param($stmt, "i", $user_id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);
$user = mysqli_fetch_assoc($result);

// Initialize variables
$success_message = "";
$error_message = "";

// Handle profile update
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST["update_profile"])) {
    $email = trim($_POST["email"]);
    $phone = trim($_POST["phone"]);
    $address = trim($_POST["address"]);
    
    // Validate email
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error_message = "Invalid email format";
    } else {
        // Update user profile
        $update_query = "UPDATE users SET email = ?, phone = ?, address = ? WHERE user_id = ?";
        $update_stmt = mysqli_prepare($conn, $update_query);
        mysqli_stmt_bind_param($update_stmt, "sssi", $email, $phone, $address, $user_id);
        
        if (mysqli_stmt_execute($update_stmt)) {
            $success_message = "Profile updated successfully";
            
            // Refresh user data
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $user = mysqli_fetch_assoc($result);
        } else {
            $error_message = "Error updating profile: " . mysqli_error($conn);
        }
    }
}

// Handle password change
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST["change_password"])) {
    $current_password = $_POST["current_password"];
    $new_password = $_POST["new_password"];
    $confirm_password = $_POST["confirm_password"];
    
    // Verify current password
    if (!password_verify($current_password, $user["password"])) {
        $error_message = "Current password is incorrect";
    } elseif (strlen($new_password) < 8) {
        $error_message = "New password must be at least 8 characters long";
    } elseif ($new_password !== $confirm_password) {
        $error_message = "New passwords do not match";
    } else {
        // Hash the new password
        $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
        
        // Update password
        $update_query = "UPDATE users SET password = ? WHERE user_id = ?";
        $update_stmt = mysqli_prepare($conn, $update_query);
        mysqli_stmt_bind_param($update_stmt, "si", $hashed_password, $user_id);
        
        if (mysqli_stmt_execute($update_stmt)) {
            $success_message = "Password changed successfully";
        } else {
            $error_message = "Error changing password: " . mysqli_error($conn);
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include('CSS/header.php'); ?>
    <title>My Profile | Student Dashboard</title>
    <style>
        :root {
            --primary-color: #175883;
            --primary-dark: #0d3c60;
            --primary-light: #2980b9;
            --accent-color: #cbb09c;
            --text-light: #ffffff;
            --text-dark: #333333;
            --bg-light: #f8f9fa;
            --bg-dark: #2d3a4b;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
        }
        
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            color: var(--text-dark);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            margin-left: 270px;
            transition: margin-left 0.3s ease;
        }

        @media (max-width: 768px) {
            .container {
                margin-left: 0;
                padding: 15px;
            }
        }
        
        h2 {
            color: var(--primary-color);
            margin-bottom: 20px;
            font-size: 24px;
            border-bottom: 2px solid var(--primary-light);
            padding-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .profile-container {
            display: flex;
            gap: 30px;
            flex-wrap: wrap;
        }
        
        .profile-sidebar {
            flex: 1;
            min-width: 250px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .profile-header {
            background: var(--primary-color);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .profile-avatar {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: var(--primary-light);
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
            color: white;
        }
        
        .profile-name {
            font-size: 20px;
            font-weight: 500;
            margin: 0 0 5px 0;
        }
        
        .profile-role {
            font-size: 14px;
            opacity: 0.9;
            margin: 0;
        }
        
        .profile-details {
            padding: 20px;
        }
        
        .profile-detail {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
            color: var(--text-dark);
        }
        
        .profile-detail i {
            color: var(--primary-color);
            width: 20px;
        }
        
        .profile-content {
            flex: 2;
            min-width: 300px;
        }
        
        .card {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .card-header {
            background: var(--primary-color);
            color: white;
            padding: 15px 20px;
            font-size: 18px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .card-body {
            padding: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: var(--text-dark);
        }
        
        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        
        .form-group input:focus {
            border-color: var(--primary-color);
            outline: none;
            box-shadow: 0 0 0 2px rgba(23, 88, 131, 0.2);
        }
        
        .btn {
            display: inline-block;
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: background-color 0.3s;
        }
        
        .btn:hover {
            background-color: var(--primary-dark);
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .alert-success {
            background-color: rgba(46, 204, 113, 0.2);
            color: #27ae60;
        }
        
        .alert-danger {
            background-color: rgba(231, 76, 60, 0.2);
            color: #c0392b;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 0 10px;
                margin: 20px auto;
            }
            
            .profile-container {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>

<?php
$user_role = $_SESSION["role"];
if ($user_role === 'lecturer') {
    include('CSS/lecturersidebar.php');
} else {
    include('CSS/studentsidebar.php');
}
?>

<div class="container">
    <h2><i class="fas fa-user-circle"></i> My Profile</h2>
    
    <?php if (!empty($success_message)): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
        </div>
    <?php endif; ?>
    
    <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle"></i> <?php echo $error_message; ?>
        </div>
    <?php endif; ?>
    
    <div class="profile-container">
        <div class="profile-sidebar">
            <div class="profile-header">
                <div class="profile-avatar">
                    <?php echo strtoupper(substr($user["full_name"], 0, 1)); ?>
                </div>
                <h3 class="profile-name"><?php echo htmlspecialchars($user["full_name"]); ?></h3>
                <p class="profile-role"><?php echo ucfirst(htmlspecialchars($user["role"])); ?></p>
            </div>
            <div class="profile-details">
                <div class="profile-detail">
                    <i class="fas fa-id-card"></i>
                    <span>ID: <?php echo htmlspecialchars($user["username"]); ?></span>
                </div>
                
                <?php if (!empty($user["email"])): ?>
                <div class="profile-detail">
                    <i class="fas fa-envelope"></i>
                    <span><?php echo htmlspecialchars($user["email"]); ?></span>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($user["phone"])): ?>
                <div class="profile-detail">
                    <i class="fas fa-phone"></i>
                    <span><?php echo htmlspecialchars($user["phone"]); ?></span>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($user["course"])): ?>
                <div class="profile-detail">
                    <i class="fas fa-graduation-cap"></i>
                    <span><?php echo htmlspecialchars($user["course"]); ?></span>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($user["year"])): ?>
                <div class="profile-detail">
                    <i class="fas fa-calendar-alt"></i>
                    <span>Year <?php echo htmlspecialchars($user["year"]); ?></span>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($user["stream"])): ?>
                <div class="profile-detail">
                    <i class="fas fa-stream"></i>
                    <span>Stream <?php echo htmlspecialchars($user["stream"]); ?></span>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($user["address"])): ?>
                <div class="profile-detail">
                    <i class="fas fa-map-marker-alt"></i>
                    <span><?php echo htmlspecialchars($user["address"]); ?></span>
                </div>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="profile-content">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-user-edit"></i> Edit Profile
                </div>
                <div class="card-body">
                    <form method="post" action="">
                        <div class="form-group">
                            <label for="email">Email Address</label>
                            <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($user["email"] ?? ''); ?>" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="phone">Phone Number</label>
                            <input type="text" id="phone" name="phone" value="<?php echo htmlspecialchars($user["phone"] ?? ''); ?>">
                        </div>
                        
                        <div class="form-group">
                            <label for="address">Address</label>
                            <input type="text" id="address" name="address" value="<?php echo htmlspecialchars($user["address"] ?? ''); ?>">
                        </div>
                        
                        <button type="submit" name="update_profile" class="btn">
                            <i class="fas fa-save"></i> Save Changes
                        </button>
                    </form>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-lock"></i> Change Password
                </div>
                <div class="card-body">
                    <form method="post" action="">
                        <div class="form-group">
                            <label for="current_password">Current Password</label>
                            <input type="password" id="current_password" name="current_password" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="new_password">New Password</label>
                            <input type="password" id="new_password" name="new_password" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="confirm_password">Confirm New Password</label>
                            <input type="password" id="confirm_password" name="confirm_password" required>
                        </div>
                        
                        <button type="submit" name="change_password" class="btn">
                            <i class="fas fa-key"></i> Change Password
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Add active class to current page link
    document.addEventListener('DOMContentLoaded', function() {
        const currentPage = window.location.pathname.split('/').pop();
        const menuLinks = document.querySelectorAll('#menu a');
        
        menuLinks.forEach(link => {
            const linkPage = link.getAttribute('href');
            if (linkPage === currentPage) {
                link.classList.add('active');
            }
        });
    });
</script>

</body>
</html>

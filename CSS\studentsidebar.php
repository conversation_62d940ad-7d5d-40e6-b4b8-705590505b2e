 
<style>
    /* Sidebar Styles */
    .sidebar {
        position: fixed;
        top: 0;
        left: 0;
        height: 100%;
        width: 250px;
        background-color: var(--bg-dark);
        color: var(--text-light);
        z-index: 1000;
        transition: all 0.3s;
        overflow-y: auto;
    }
    
    .sidebar-header {
        padding: 20px;
        background-color: var(--primary-dark);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    
    .sidebar-logo {
        display: flex;
        align-items: center;
        gap: 10px;
        color: white;
        text-decoration: none;
    }
    
    .sidebar-logo img {
        width: 40px;
        height: 40px;
    }
    
    .sidebar-logo h1 {
        font-size: 18px;
        margin: 0;
        font-weight: 500;
    }
    
    .sidebar-toggle {
        display: none;
        background: none;
        border: none;
        color: white;
        font-size: 20px;
        cursor: pointer;
    }
    
    .user-info {
        padding: 15px 20px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: var(--primary-color);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        color: white;
    }
    
    .user-details {
        flex: 1;
        overflow: hidden;
    }
    
    .user-name {
        font-size: 14px;
        font-weight: 500;
        margin: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    
    .user-role {
        font-size: 12px;
        opacity: 0.7;
        margin: 0;
    }
    
    #menu {
        list-style: none;
        padding: 0;
        margin: 0;
    }
    
    #menu li {
        margin: 0;
    }
    
    #menu a {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 15px 20px;
        color: var(--text-light);
        text-decoration: none;
        transition: all 0.3s;
        border-left: 3px solid transparent;
    }
    
    #menu a:hover {
        background-color: rgba(255, 255, 255, 0.1);
        border-left-color: var(--primary-light);
    }
    
    #menu a.active {
        background-color: rgba(255, 255, 255, 0.1);
        border-left-color: var(--primary-light);
        font-weight: 500;
    }

    #menu a.highlight-link {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
        border-left-color: var(--accent-color);
        font-weight: 600;
        position: relative;
    }

    #menu a.highlight-link::after {
        content: '';
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        width: 8px;
        height: 8px;
        background: var(--accent-color);
        border-radius: 50%;
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0%, 100% { opacity: 0.6; transform: translateY(-50%) scale(1); }
        50% { opacity: 1; transform: translateY(-50%) scale(1.2); }
    }

    #menu a.highlight-link:hover {
        background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
        transform: translateX(5px);
    }

    #menu a i {
        width: 20px;
        text-align: center;
    }
    
    .menu-section {
        padding: 10px 20px;
        font-size: 12px;
        text-transform: uppercase;
        color: rgba(255, 255, 255, 0.5);
        margin-top: 15px;
    }
    
    .sidebar-footer {
        padding: 15px 20px;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        position: sticky;
        bottom: 0;
        background-color: var(--bg-dark);
    }
    
    .sidebar-footer a {
        display: flex;
        align-items: center;
        gap: 10px;
        color: var(--text-light);
        text-decoration: none;
        opacity: 0.7;
        transition: opacity 0.3s;
    }
    
    .sidebar-footer a:hover {
        opacity: 1;
    }
    
    /* Main Content Styles */
    .main-content {
        margin-left: 250px;
        padding: 20px;
        transition: all 0.3s;
    }
    
    /* Mobile Styles */
    @media (max-width: 768px) {
        .sidebar {
            transform: translateX(-100%);
        }
        
        .sidebar.active {
            transform: translateX(0);
        }
        
        .sidebar-toggle {
            display: block;
        }
        
        .main-content {
            margin-left: 0;
        }
        
        .mobile-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px;
            background-color: var(--primary-color);
            color: white;
            position: sticky;
            top: 0;
            z-index: 999;
        }
        
        .mobile-logo {
            display: flex;
            align-items: center;
            gap: 10px;
            color: white;
            text-decoration: none;
        }
        
        .mobile-logo img {
            width: 30px;
            height: 30px;
        }
        
        .mobile-logo h1 {
            font-size: 16px;
            margin: 0;
            font-weight: 500;
        }
        
        .mobile-toggle {
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
        }
    }
</style>

<?php
// Get user details
$user_query = "SELECT * FROM users WHERE user_id = ?";
$user_stmt = mysqli_prepare($conn, $user_query);
mysqli_stmt_bind_param($user_stmt, "i", $user_id);
mysqli_stmt_execute($user_stmt);
$user_result = mysqli_stmt_get_result($user_stmt);
$user_data = mysqli_fetch_assoc($user_result);

// Get first letter of first name for avatar
$first_letter = substr($user_data['firstname'], 0, 1);
?>

<!-- Mobile Header (visible on small screens) -->
<div class="mobile-header">
    <a href="student_dashboard.php" class="mobile-logo">
        <i class="fas fa-university"></i>
        <h1>Venue Management</h1>
    </a>
    <button class="mobile-toggle" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>
</div>

<!-- Sidebar -->
<div class="sidebar" id="sidebar">
    <div class="sidebar-header">
        <a href="student_dashboard.php" class="sidebar-logo">
            <i class="fas fa-university"></i>
            <h1>Venue Management</h1>
        </a>
        <button class="sidebar-toggle" onclick="toggleSidebar()">
            <i class="fas fa-times"></i>
        </button>
    </div>
    
    <div class="user-info">
        <div class="user-avatar">
            <?php echo $first_letter; ?>
        </div>
        <div class="user-details">
            <p class="user-name"><?php echo htmlspecialchars($user_data['firstname'] . ' ' . $user_data['lastname']); ?></p>
            <p class="user-role">Student</p>
        </div>
    </div>
    
    <ul id="menu">
        <li>
            <a href="student_dashboard.php">
                <i class="fas fa-tachometer-alt"></i> Dashboard
            </a>
        </li>

        <div class="menu-section">Attendance</div>
        <li>
            <a href="scan_venue.php" class="highlight-link">
                <i class="fas fa-qrcode"></i> Scan QR Code
            </a>
        </li>
        <li>
            <a href="venue_history.php">
                <i class="fas fa-history"></i> My Attendance
            </a>
        </li>

        <div class="menu-section">Academic</div>
        <li>
            <a href="student_schedule.php">
                <i class="fas fa-calendar-day"></i> Class Schedule
            </a>
        </li>
        <li>
            <a href="free_venues.php">
                <i class="fas fa-door-open"></i> Available Venues
            </a>
        </li>

        <div class="menu-section">Account</div>
        <li>
            <a href="profile.php">
                <i class="fas fa-user"></i> My Profile
            </a>
        </li>
        <li>
            <a href="notifications.php">
                <i class="fas fa-bell"></i> Notifications
            </a>
        </li>
    </ul>
    
    <div class="sidebar-footer">
        <a href="logout.php">
            <i class="fas fa-sign-out-alt"></i> Logout
        </a>
    </div>
</div>

<script>
    function toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        sidebar.classList.toggle('active');
    }
    
    // Close sidebar when clicking outside on mobile
    document.addEventListener('click', function(event) {
        const sidebar = document.getElementById('sidebar');
        const mobileToggle = document.querySelector('.mobile-toggle');
        const sidebarToggle = document.querySelector('.sidebar-toggle');
        
        if (window.innerWidth <= 768 && 
            !sidebar.contains(event.target) && 
            event.target !== mobileToggle &&
            event.target !== sidebarToggle) {
            sidebar.classList.remove('active');
        }
    });
    
    // Adjust for mobile view
    window.addEventListener('resize', function() {
        const sidebar = document.getElementById('sidebar');
        if (window.innerWidth > 768) {
            sidebar.classList.remove('active');
        }
    });
</script>

<?php
// Check if the QR library is already included
if (!class_exists('QRcode')) {
    // Include the PHP QR Code library only if not already included
    require_once 'qrlib.php';
}

// Include simple QR as fallback
require_once 'simple_qr.php';

// Function to generate QR code and save the image file
function generateQRCode($data, $venueName, $venueId = null)
{
    // Clean the venue name for use as filename
    $cleanVenueName = cleanFilename($venueName);

    // Create filename using venue name
    $filename = $cleanVenueName;

    // Add venue ID if provided for uniqueness
    if ($venueId) {
        $filename = $cleanVenueName . '_ID' . $venueId;
    }

    // Create a temporary directory for QR code generation
    $tempDir = sys_get_temp_dir();

    // Generate QR code file in temp directory
    $filePath = $tempDir . '/' . $filename . '.png';

    // Try to use the main QR library first
    try {
        if (class_exists('QRcode')) {
            QRcode::png($data, $filePath, QR_ECLEVEL_L, 10, 2);
        } else {
            // Fallback to SimpleQR if main library is not available
            SimpleQR::png($data, $filePath, 400);
        }

        // Check if file was created successfully
        if (!file_exists($filePath)) {
            throw new Exception("Failed to generate QR code file in temporary directory");
        }
    } catch (Exception $e) {
        // If both fail, try a simple approach
        SimpleQR::png($data, $filePath, 400);
        if (!file_exists($filePath)) {
            throw new Exception("All QR generation methods failed: " . $e->getMessage());
        }
    }

    // Use the specified desktop path
    $desktopPath = 'D:\Elimu\\';

    // Make sure the desktop directory exists and is writable
    if (!is_dir($desktopPath)) {
        throw new Exception("Desktop directory doesn't exist: " . $desktopPath);
    }

    if (!is_writable($desktopPath)) {
        throw new Exception("Desktop directory is not writable: " . $desktopPath);
    }

    // Full path for the QR code on desktop
    $desktopFilePath = $desktopPath . $filename . '.png';

    // Copy the file to desktop
    if (!copy($filePath, $desktopFilePath)) {
        throw new Exception("Failed to copy QR code to desktop. Check permissions.");
    }

    // Delete the temporary QR code file
    unlink($filePath);

    return $desktopFilePath;
}

// Function to clean venue name for use as filename
function cleanFilename($filename) {
    // Remove or replace invalid characters for Windows filenames
    $invalid_chars = array('<', '>', ':', '"', '/', '\\', '|', '?', '*');
    $filename = str_replace($invalid_chars, '_', $filename);

    // Remove extra spaces and replace with underscores
    $filename = preg_replace('/\s+/', '_', trim($filename));

    // Remove any trailing dots or spaces
    $filename = rtrim($filename, '. ');

    // Limit filename length to 100 characters
    if (strlen($filename) > 100) {
        $filename = substr($filename, 0, 100);
    }

    // Ensure filename is not empty
    if (empty($filename)) {
        $filename = 'venue_qr_' . time();
    }

    return $filename;
}

// Function to get desktop path based on operating system
function getDesktopPath()
{
    // For Windows
    if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
        // Try multiple methods to find the desktop
        
        // Method 1: Using environment variables
        $desktopPath = getenv('USERPROFILE') . DIRECTORY_SEPARATOR . 'Desktop';
        if (is_dir($desktopPath)) {
            return $desktopPath;
        }
        
        // Method 2: Using HOME environment variable
        $desktopPath = getenv('HOMEDRIVE') . getenv('HOMEPATH') . DIRECTORY_SEPARATOR . 'Desktop';
        if (is_dir($desktopPath)) {
            return $desktopPath;
        }
        
        // Method 3: Using the current user's profile
        $username = get_current_user();
        $desktopPath = 'C:' . DIRECTORY_SEPARATOR . 'Users' . DIRECTORY_SEPARATOR . $username . DIRECTORY_SEPARATOR . 'Desktop';
        if (is_dir($desktopPath)) {
            return $desktopPath;
        }
    } 
    // For Linux
    else if (strtoupper(substr(PHP_OS, 0, 5)) === 'LINUX') {
        $desktopPath = getenv('HOME') . DIRECTORY_SEPARATOR . 'Desktop';
        if (is_dir($desktopPath)) {
            return $desktopPath;
        }
    } 
    // For macOS
    else if (strtoupper(substr(PHP_OS, 0, 6)) === 'DARWIN') {
        $desktopPath = getenv('HOME') . DIRECTORY_SEPARATOR . 'Desktop';
        if (is_dir($desktopPath)) {
            return $desktopPath;
        }
    }
    
    // If we couldn't determine the desktop path
    return false;
}
?>

<?php
session_start();

// Check if the user is logged in
if (!isset($_SESSION["user"]) || $_SESSION["role"] !== "lecturer") {
    // Redirect unauthorized users to login page
    header("Location: login.php");
    exit();
}

// Get lecturer name from session
$lecturerName = isset($_SESSION["name"]) ? $_SESSION["name"] : "Lecturer";

// Database connection
require_once "database.php";

// Get venue usage history (recent check-ins)
$sqlHistory = "SELECT v.venuename, c.check_in_time, c.check_out_time, c.id as checkin_id 
               FROM venue_checkins c
               JOIN venues v ON c.venue_id = v.venueid
               WHERE c.user_id = ?
               ORDER BY c.check_in_time DESC LIMIT 5";
$stmt = mysqli_prepare($conn, $sqlHistory);
mysqli_stmt_bind_param($stmt, "i", $_SESSION["user"]);
mysqli_stmt_execute($stmt);
$resultHistory = mysqli_stmt_get_result($stmt);

// Get active check-in (if any)
$sqlActiveCheckin = "SELECT c.*, v.venuename, v.venueid 
                    FROM venue_checkins c
                    JOIN venues v ON c.venue_id = v.venueid
                    WHERE c.user_id = ? AND c.check_out_time IS NULL
                    ORDER BY c.check_in_time DESC LIMIT 1";
$stmt = mysqli_prepare($conn, $sqlActiveCheckin);
mysqli_stmt_bind_param($stmt, "i", $_SESSION["user"]);
mysqli_stmt_execute($stmt);
$resultActiveCheckin = mysqli_stmt_get_result($stmt);
$activeCheckin = mysqli_fetch_assoc($resultActiveCheckin);

// Get notifications
$sqlNotifications = "SELECT * FROM notifications 
                    WHERE user_id = ? 
                    ORDER BY created_at DESC LIMIT 5";
$stmt = mysqli_prepare($conn, $sqlNotifications);
mysqli_stmt_bind_param($stmt, "i", $_SESSION["user"]);
mysqli_stmt_execute($stmt);
$resultNotifications = mysqli_stmt_get_result($stmt);
$notificationCount = mysqli_num_rows($resultNotifications);

// Get available venues
$sqlAvailableVenues = "SELECT * FROM venues WHERE status = 'available' OR status = 'free' LIMIT 5";
$resultAvailableVenues = mysqli_query($conn, $sqlAvailableVenues);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include('CSS/header.php'); ?>
    <style>
        :root {
            --primary-color: #175883;
            --primary-dark: #0d3c60;
            --primary-light: #2980b9;
            --accent-color: #cbb09c;
            --text-light: #ffffff;
            --text-dark: #333333;
            --bg-light: #f8f9fa;
            --bg-dark: #2d3a4b;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #3498db;
        }
        
        body {
            background-color: var(--bg-light);
            font-family: 'Roboto', sans-serif;
            margin: 0;
            padding: 0;
        }
        
        .dashboard-container {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 15px;
            transition: margin-left 0.3s ease;
        }

        /* Adjust dashboard when sidebar is open */
        #menuToggle:checked ~ .dashboard-container {
            margin-left: 250px;
        }

        @media (max-width: 768px) {
            #menuToggle:checked ~ .dashboard-container {
                margin-left: 0;
                opacity: 0.4;
            }
        }
        
        .welcome-banner {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
            text-align: center;
        }
        
        .welcome-banner h1 {
            margin: 0 0 10px 0;
            font-size: 28px;
            font-weight: 500;
        }
        
        .welcome-banner p {
            margin: 0;
            font-size: 18px;
            opacity: 0.9;
        }
        
        /* Redesigned stats container */
        .stats-container {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            text-align: center;
            transition: transform 0.3s, box-shadow 0.3s;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            border-top: 4px solid var(--primary-color);
        }
        
        .stat-card:nth-child(2) {
            border-top-color: var(--success-color);
        }
        
        .stat-card:nth-child(3) {
            border-top-color: var(--warning-color);
        }
        
        .stat-card:nth-child(4) {
            border-top-color: var(--info-color);
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .stat-icon {
            font-size: 28px;
            margin-bottom: 15px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .stat-card:nth-child(1) .stat-icon {
            background-color: var(--primary-color);
        }
        
        .stat-card:nth-child(2) .stat-icon {
            background-color: var(--success-color);
        }
        
        .stat-card:nth-child(3) .stat-icon {
            background-color: var(--warning-color);
        }
        
        .stat-card:nth-child(4) .stat-icon {
            background-color: var(--info-color);
        }
        
        .stat-value {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 5px;
            color: var(--text-dark);
        }
        
        .stat-label {
            color: #777;
            font-size: 16px;
        }
        
        /* Redesigned main content layout */
        .main-content {
            display: grid;
            grid-template-columns: 1fr;
            gap: 25px;
        }
        
        /* Active check-in or QR scanner card */
        .primary-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            margin-bottom: 25px;
        }
        
        .card-header {
            background: var(--primary-color);
            color: white;
            padding: 15px 20px;
            font-size: 18px;
            font-weight: 500;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .card-header a {
            color: white;
            text-decoration: none;
            font-size: 14px;
            opacity: 0.8;
            transition: opacity 0.3s;
        }
        
        .card-header a:hover {
            opacity: 1;
        }
        
        .card-body {
            padding: 20px;
        }
        
        /* Secondary cards layout */
        .secondary-cards {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 25px;
        }
        
        .dashboard-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            height: fit-content;
        }
        
        /* Active check-in styling */
        .active-checkin {
            border: none;
            background-color: white;
        }
        
        .active-venue {
            text-align: center;
            padding: 25px;
        }
        
        .active-venue h3 {
            color: var(--primary-color);
            font-size: 24px;
            margin-bottom: 15px;
        }
        
        .check-in-time, .duration {
            margin: 10px 0;
            color: var(--text-dark);
        }
        
        .checkout-form {
            margin-top: 20px;
        }
        
        .checkout-button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 30px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s;
            width: 100%;
            max-width: 200px;
        }
        
        .checkout-button:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }
        
        /* QR scanner styling */
        .qr-scanner-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 25px;
        }
        
        #qr-video-container {
            width: 100%;
            max-width: 300px;
            height: 300px;
            margin: 20px 0;
            border-radius: 12px;
            overflow: hidden;
            border: 1px solid #eee;
        }
        
        #qr-video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        #scan-button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 30px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 15px;
        }
        
        #scan-button:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }
        
        #scan-button i {
            margin-right: 8px;
        }
        
        /* History list styling */
        .history-list {
            display: flex;
            flex-direction: column;
        }
        
        .history-item {
            padding: 15px;
            border-bottom: 1px solid #eee;
            transition: background-color 0.3s;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .history-item:last-child {
            border-bottom: none;
        }
        
        .history-item:hover {
            background-color: rgba(41, 128, 185, 0.05);
        }
        
        .history-venue {
            font-weight: 500;
            font-size: 16px;
            color: var(--primary-color);
            margin-bottom: 5px;
        }
        
        .history-time {
            font-size: 14px;
            color: #666;
        }
        
        .history-time i {
            margin-right: 5px;
        }
        
        /* Notification list styling */
        .notification-list {
            display: flex;
            flex-direction: column;
        }
        
        .notification-item {
            padding: 15px;
            border-bottom: 1px solid #eee;
            transition: background-color 0.3s;
            border-left: 3px solid transparent;
        }
        
        .notification-item:last-child {
            border-bottom: none;
        }
        
        .notification-item:hover {
            background-color: rgba(41, 128, 185, 0.05);
        }
        
        .notification-item.warning {
            border-left-color: var(--warning-color);
        }
        
        .notification-item.info {
            border-left-color: var(--info-color);
        }
        
        .notification-item.success {
            border-left-color: var(--success-color);
        }
        
        .notification-item.danger {
            border-left-color: var(--danger-color);
        }
        
        .notification-title {
            font-weight: 500;
            font-size: 16px;
            margin-bottom: 5px;
            display: flex;
            align-items: center;
        }
        
        .notification-title i {
            margin-right: 8px;
        }
        
        .notification-content {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
        }
        
        .notification-time {
            font-size: 12px;
            color: #999;
            text-align: right;
        }
        
        /* Venue list styling */
        .venue-list {
            display: flex;
            flex-direction: column;
        }
        
        .venue-item {
            display: flex;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid #eee;
            transition: background-color 0.3s;
        }
        
        .venue-item:last-child {
            border-bottom: none;
        }
        
        .venue-item:hover {
            background-color: rgba(41, 128, 185, 0.05);
        }
        
        .venue-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: var(--success-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: 15px;
            flex-shrink: 0;
        }
        
        .venue-details {
            flex-grow: 1;
        }
        
        .venue-name {
            font-weight: 500;
            font-size: 16px;
            color: var(--text-dark);
            margin: 0 0 5px 0;
        }
        
        .venue-location {
            font-size: 14px;
            color: #666;
            margin: 0;
            display: flex;
            align-items: center;
        }
        
        .venue-location i {
            margin-right: 5px;
            font-size: 12px;
        }
        
        .venue-action {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-decoration: none;
            transition: all 0.3s;
        }
        
        .venue-action:hover {
            background-color: var(--primary-dark);
            transform: scale(1.1);
        }
        
        .empty-state {
            text-align: center;
            padding: 30px 20px;
            color: #666;
        }
        
        .empty-state i {
            font-size: 40px;
            color: #ddd;
            margin-bottom: 10px;
        }
        
        .empty-state p {
            margin: 0;
        }

        /* Success Popup Styles */
        .success-popup-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .success-popup-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .success-popup {
            background: white;
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            transform: scale(0.7);
            transition: transform 0.3s ease;
            max-width: 400px;
            width: 90%;
        }

        .success-popup-overlay.show .success-popup {
            transform: scale(1);
        }

        .success-tick {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: var(--success-color);
            margin: 0 auto 20px;
            position: relative;
            animation: successPulse 0.6s ease-out;
        }

        .success-tick::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 25px;
            height: 15px;
            border: 3px solid white;
            border-top: none;
            border-right: none;
            transform: translate(-50%, -60%) rotate(-45deg);
            animation: tickDraw 0.4s ease-out 0.2s both;
        }

        @keyframes successPulse {
            0% {
                transform: scale(0);
                opacity: 0;
            }
            50% {
                transform: scale(1.1);
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        @keyframes tickDraw {
            0% {
                width: 0;
                height: 0;
            }
            50% {
                width: 25px;
                height: 0;
            }
            100% {
                width: 25px;
                height: 15px;
            }
        }

        .success-popup h2 {
            color: var(--success-color);
            margin: 0 0 15px 0;
            font-size: 24px;
            font-weight: 600;
        }

        .success-popup p {
            color: #666;
            margin: 0 0 20px 0;
            font-size: 16px;
            line-height: 1.5;
        }

        .venue-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
        }

        .venue-info strong {
            color: var(--primary-color);
            display: block;
            font-size: 18px;
            margin-bottom: 5px;
        }

        .countdown {
            color: #999;
            font-size: 14px;
            margin-top: 15px;
        }

        .countdown-number {
            color: var(--primary-color);
            font-weight: bold;
        }

        @media (max-width: 992px) {
            .secondary-cards {
                grid-template-columns: 1fr;
            }
        }
        
        @media (max-width: 768px) {
            .stats-container {
                grid-template-columns: 1fr 1fr;
            }
            
            .history-time {
                flex-direction: column;
                align-items: flex-start;
            }
        }
        
        @media (max-width: 576px) {
            .stats-container {
                grid-template-columns: 1fr;
            }
        }
        
        /* Add styles for the simplified QR scan button */
        .quick-scan-button-container {
            display: flex;
            justify-content: center;
            margin: 20px 0 30px 0;
        }

        .quick-scan-button {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            text-decoration: none;
            box-shadow: 0 4px 15px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
        }

        .quick-scan-button:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        .quick-scan-button i {
            font-size: 36px;
            margin-bottom: 8px;
        }

        .quick-scan-button span {
            font-size: 14px;
            font-weight: 500;
        }

        /* Styles for sidebar notifications */
        .sidebar-notifications {
            background: white;
            margin: 15px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .sidebar-notification-header {
            background: var(--primary-color);
            color: white;
            padding: 12px 15px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .notification-count {
            background: white;
            color: var(--primary-color);
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            margin-left: auto;
        }

        .sidebar-notification-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .sidebar-notification-item {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
            border-left: 3px solid transparent;
        }

        .sidebar-notification-item.warning {
            border-left-color: var(--warning-color);
        }

        .sidebar-notification-item.info {
            border-left-color: var(--info-color);
        }

        .sidebar-notification-item.success {
            border-left-color: var(--success-color);
        }

        .sidebar-notification-item.danger {
            border-left-color: var(--danger-color);
        }

        .sidebar-notification-item .notification-title {
            font-size: 13px;
            margin-bottom: 3px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .sidebar-notification-item .notification-time {
            font-size: 11px;
            color: #999;
        }

        .empty-notification {
            padding: 15px;
            text-align: center;
            color: #999;
            font-size: 13px;
        }

        .view-all-notifications {
            display: block;
            padding: 10px 15px;
            text-align: center;
            background: #f8f9fa;
            color: var(--primary-color);
            text-decoration: none;
            font-size: 13px;
            font-weight: 500;
            transition: background-color 0.3s;
        }

        .view-all-notifications:hover {
            background: #eee;
        }
    </style>
</head>
<body>

<?php include('CSS/lecturersidebar.php'); ?>

<div class="dashboard-container">
    <div class="welcome-banner">
        <h1>Welcome, <?php echo htmlspecialchars($lecturerName); ?>!</h1>
        <p>Manage your classes and track your venue usage.</p>
    </div>
    
    <div class="stats-container">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-history"></i>
            </div>
            <div class="stat-value"><?php echo mysqli_num_rows($resultHistory); ?></div>
            <div class="stat-label">Recent Check-ins</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-bell"></i>
            </div>
            <div class="stat-value"><?php echo $notificationCount; ?></div>
            <div class="stat-label">Notifications</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="stat-value">
                <?php 
                // Get student attendance count for active venue
                if ($activeCheckin) {
                    $sqlAttendance = "SELECT COUNT(*) as count FROM venue_checkins 
                                     WHERE venue_id = ? AND check_in_time > ? AND user_id != ?";
                    $stmt = mysqli_prepare($conn, $sqlAttendance);
                    mysqli_stmt_bind_param($stmt, "isi", $activeCheckin['venue_id'], $activeCheckin['check_in_time'], $_SESSION["user"]);
                    mysqli_stmt_execute($stmt);
                    $attendanceResult = mysqli_stmt_get_result($stmt);
                    $attendanceRow = mysqli_fetch_assoc($attendanceResult);
                    echo $attendanceRow['count'];
                } else {
                    echo "0";
                }
                ?>
            </div>
            <div class="stat-label">Student Attendance</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-building"></i>
            </div>
            <div class="stat-value"><?php echo mysqli_num_rows($resultAvailableVenues); ?></div>
            <div class="stat-label">Available Venues</div>
        </div>
    </div>
    
    <div class="main-content">
        <!-- Primary Card: Active Check-in or QR Scanner -->
        <?php if ($activeCheckin): ?>
        <!-- Active Check-in Card -->
        <div class="primary-card">
            <div class="card-header">
                <span><i class="fas fa-check-circle"></i> Currently Active Venue</span>
            </div>
            <div class="card-body">
                <div class="active-venue">
                    <h3><?php echo htmlspecialchars($activeCheckin['venuename']); ?></h3>
                    <p class="check-in-time">
                        <i class="fas fa-clock"></i> Checked in at: 
                        <?php echo date("h:i A", strtotime($activeCheckin['check_in_time'])); ?>
                        (<?php echo date("M d, Y", strtotime($activeCheckin['check_in_time'])); ?>)
                    </p>
                    <p class="duration">
                        <i class="fas fa-hourglass-half"></i> Duration: 
                        <?php 
                            $checkin_time = new DateTime($activeCheckin['check_in_time']);
                            $current_time = new DateTime();
                            $interval = $checkin_time->diff($current_time);
                            
                            if ($interval->h > 0) {
                                echo $interval->format("%h hours, %i minutes");
                            } else {
                                echo $interval->format("%i minutes");
                            }
                        ?>
                    </p>
                    
                    <form action="process_venue_scan.php" method="post" class="checkout-form">
                        <input type="hidden" name="checkin_id" value="<?php echo $activeCheckin['id']; ?>">
                        <input type="hidden" name="venue_id" value="<?php echo $activeCheckin['venueid']; ?>">
                        <input type="hidden" name="action" value="checkout">
                        <button type="submit" class="checkout-button">
                            <i class="fas fa-sign-out-alt"></i> Check Out
                        </button>
                    </form>
                </div>
            </div>
        </div>
        <?php else: ?>
        <!-- Quick Venue Check-in Icon Button (Simplified) -->
        <div class="quick-scan-button-container">
            <a href="scan_venue.php" class="quick-scan-button">
                <i class="fas fa-qrcode"></i>
                <span>Scan QR Code</span>
            </a>
        </div>
        <?php endif; ?>
        
        <!-- Secondary Cards in 2-column layout -->
        <div class="secondary-cards">
            <!-- Recent Venue Usage Card -->
            <div class="dashboard-card">
                <div class="card-header">
                    <span><i class="fas fa-history"></i> Recent Venue Usage</span>
                    <a href="venue_history.php"><i class="fas fa-external-link-alt"></i> View All</a>
                </div>
                <div class="card-body">
                    <?php if (mysqli_num_rows($resultHistory) > 0): ?>
                        <div class="history-list">
                            <?php while ($history = mysqli_fetch_assoc($resultHistory)): ?>
                                <div class="history-item">
                                    <div>
                                        <div class="history-venue"><?php echo htmlspecialchars($history['venuename']); ?></div>
                                        <div class="history-time">
                                            <i class="fas fa-sign-in-alt"></i> <?php echo date("M d, h:i A", strtotime($history['check_in_time'])); ?>
                                        </div>
                                    </div>
                                    <div class="history-time">
                                        <?php 
                                            if ($history['check_out_time']) {
                                                echo '<i class="fas fa-sign-out-alt"></i> ' . date("M d, h:i A", strtotime($history['check_out_time']));
                                            } else {
                                                echo '<span style="color: var(--warning-color);"><i class="fas fa-exclamation-circle"></i> Not checked out</span>';
                                            }
                                        ?>
                                    </div>
                                </div>
                            <?php endwhile; ?>
                        </div>
                    <?php else: ?>
                        <div class="empty-state">
                            <i class="fas fa-history"></i>
                            <p>No recent venue usage history found.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Available Venues Card -->
            <div class="dashboard-card">
                <div class="card-header">
                    <span><i class="fas fa-building"></i> Available Venues</span>
                    <a href="venues.php"><i class="fas fa-external-link-alt"></i> View All</a>
                </div>
                <div class="card-body">
                    <?php if (mysqli_num_rows($resultAvailableVenues) > 0): ?>
                        <div class="venue-list">
                            <?php while ($venue = mysqli_fetch_assoc($resultAvailableVenues)): ?>
                                <div class="venue-item">
                                    <div class="venue-icon">
                                        <i class="fas fa-check"></i>
                                    </div>
                                    <div class="venue-details">
                                        <h4 class="venue-name"><?php echo htmlspecialchars($venue['venuename']); ?></h4>
                                        <p class="venue-location">
                                            <i class="fas fa-map-marker-alt"></i>
                                            <?php echo htmlspecialchars($venue['location']); ?>
                                        </p>
                                    </div>
                                    <a href="scan_venue.php?venue_id=<?php echo $venue['venueid']; ?>" class="venue-action">
                                        <i class="fas fa-sign-in-alt"></i>
                                    </a>
                                </div>
                            <?php endwhile; ?>
                        </div>
                    <?php else: ?>
                        <div class="empty-state">
                            <i class="fas fa-building"></i>
                            <p>No available venues at this time.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include('CSS/footer.php'); ?>

<script src="https://unpkg.com/html5-qrcode"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const cameraSelect = document.getElementById('camera-select');
    const scanButton = document.getElementById('scan-button');
    const scanResult = document.getElementById('scan-result');
    let html5QrCode;
    let isScanning = false;
    
    // Initialize QR scanner
    html5QrCode = new Html5Qrcode("qr-video-container");
    
    // Get available cameras
    Html5Qrcode.getCameras().then(devices => {
        if (devices && devices.length) {
            cameraSelect.innerHTML = '';
            devices.forEach(device => {
                const option = document.createElement('option');
                option.value = device.id;
                option.text = device.label || `Camera ${cameraSelect.options.length + 1}`;
                cameraSelect.appendChild(option);
            });
        }
    }).catch(err => {
        console.error('Error getting cameras', err);
    });
    
    // Handle scan button click
    scanButton.addEventListener('click', function() {
        if (isScanning) {
            html5QrCode.stop().then(() => {
                isScanning = false;
                scanButton.innerHTML = '<i class="fas fa-qrcode"></i> Start Scanning';
            }).catch(err => {
                console.error('Error stopping scanner', err);
            });
        } else {
            const cameraId = cameraSelect.value;
            if (!cameraId) {
                alert('Please select a camera');
                return;
            }
            
            const config = { fps: 10 };
            html5QrCode.start(
                cameraId, 
                config,
                (decodedText) => {
                    // On successful scan
                    html5QrCode.stop();
                    isScanning = false;
                    scanButton.innerHTML = '<i class="fas fa-qrcode"></i> Start Scanning';
                    
                    // Process the QR code data
                    processQrCode(decodedText);
                },
                (errorMessage) => {
                    // Ignore errors during scanning
                }
            ).then(() => {
                isScanning = true;
                scanButton.innerHTML = '<i class="fas fa-stop"></i> Stop Scanning';
            }).catch(err => {
                alert(`Error starting scanner: ${err}`);
            });
        }
    });
    
    // Process QR code data
    function processQrCode(qrData) {
        scanResult.style.display = 'block';
        scanResult.innerHTML = '<div style="color: var(--info-color);">Processing QR code...</div>';
        
        try {
            // Assuming QR code contains JSON data with venue information
            const data = JSON.parse(qrData);
            
            if (!data.venue_id) {
                throw new Error('Invalid QR code format');
            }
            
            // Send data to server for processing
            fetch('process_venue_scan.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    venue_id: data.venue_id,
                    action: 'checkin'
                }),
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success popup instead of simple message
                    showSuccessPopup(data.message, data.venue || 'Unknown Venue', data.venue_location || '');
                } else {
                    scanResult.innerHTML = `<div style="color: var(--danger-color);">${data.message}</div>`;
                }
            })
            .catch(error => {
                scanResult.innerHTML = `<div style="color: var(--danger-color);">Error: ${error.message}</div>`;
            });
        } catch (e) {
            scanResult.style.display = 'block';
            scanResult.innerHTML = `<div style="color: var(--danger-color);">Invalid QR code format</div>`;
        }
    }

    // Show success popup function
    function showSuccessPopup(message, venueName, venueLocation) {
        // Get popup elements
        const overlay = document.getElementById('success-popup-overlay');
        const successMessage = document.getElementById('success-message');
        const venueNameElement = document.getElementById('venue-name');
        const venueLocationElement = document.getElementById('venue-location');
        const countdownElement = document.getElementById('countdown-number');

        // Set the message and venue details
        successMessage.textContent = message;
        venueNameElement.textContent = venueName;
        if (venueLocation) {
            venueLocationElement.textContent = venueLocation;
            venueLocationElement.style.display = 'block';
        } else {
            venueLocationElement.style.display = 'none';
        }

        // Show the popup
        overlay.classList.add('show');

        // Start countdown
        let countdown = 3;
        countdownElement.textContent = countdown;

        const countdownInterval = setInterval(() => {
            countdown--;
            countdownElement.textContent = countdown;

            if (countdown <= 0) {
                clearInterval(countdownInterval);
                // Reload page to update data
                window.location.reload();
            }
        }, 1000);

        // Allow clicking overlay to close (optional)
        overlay.addEventListener('click', function(e) {
            if (e.target === overlay) {
                clearInterval(countdownInterval);
                window.location.reload();
            }
        });
    }
});
</script>

<!-- Success Popup -->
<div id="success-popup-overlay" class="success-popup-overlay">
    <div class="success-popup">
        <div class="success-tick"></div>
        <h2>Check-in Successful!</h2>
        <p id="success-message">You have successfully checked in.</p>
        <div id="venue-info" class="venue-info">
            <strong id="venue-name">Venue Name</strong>
            <span id="venue-location">Location</span>
        </div>
        <div class="countdown">
            Refreshing in <span id="countdown-number" class="countdown-number">3</span> seconds...
        </div>
    </div>
</div>

</body>
</html>

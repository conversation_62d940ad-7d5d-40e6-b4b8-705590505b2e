<?php
session_start();

// Check if the user is logged in
if (!isset($_SESSION["user"]) || $_SESSION["role"] !== "student") {
    header("Location: login.php");
    exit();
}

// Database connection
require_once "database.php";

// Get user details
$user_id = $_SESSION["user"];
$user_query = "SELECT * FROM users WHERE user_id = ?";
$stmt = mysqli_prepare($conn, $user_query);
mysqli_stmt_bind_param($stmt, "i", $user_id);
mysqli_stmt_execute($stmt);
$user_result = mysqli_stmt_get_result($stmt);
$user_data = mysqli_fetch_assoc($user_result);

// Get recent check-ins
$checkins_query = "SELECT vc.*, v.venuename, v.location 
                  FROM venue_checkins vc 
                  JOIN venues v ON vc.venue_id = v.venueid 
                  WHERE vc.user_id = ? 
                  ORDER BY vc.check_in_time DESC 
                  LIMIT 5";
$stmt = mysqli_prepare($conn, $checkins_query);
mysqli_stmt_bind_param($stmt, "i", $user_id);
mysqli_stmt_execute($stmt);
$checkins_result = mysqli_stmt_get_result($stmt);

// Get venues where students can check in (occupied by lecturer)
$venues_query = "SELECT * FROM venues WHERE status = 'occupied' LIMIT 5";
$venues_result = mysqli_query($conn, $venues_query);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include('CSS/header.php'); ?>
    <title>Student Dashboard</title>
    <style>
        :root {
            --primary-color: #175883;
            --primary-dark: #0d3c60;
            --primary-light: #2980b9;
            --accent-color: #cbb09c;
            --text-light: #ffffff;
            --text-dark: #333333;
            --bg-light: #f8f9fa;
            --bg-dark: #2d3a4b;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #3498db;
            --border-color: #e9ecef;
            --shadow-light: 0 2px 10px rgba(0,0,0,0.08);
            --shadow-medium: 0 4px 20px rgba(0,0,0,0.12);
            --border-radius: 12px;
        }

        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            color: var(--text-dark);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            margin-left: 270px;
            transition: margin-left 0.3s ease;
        }

        @media (max-width: 768px) {
            .container {
                margin-left: 0;
                padding: 15px;
            }
        }
        
        .welcome-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
            color: white;
            padding: 40px;
            border-radius: var(--border-radius);
            margin-bottom: 40px;
            box-shadow: var(--shadow-medium);
            position: relative;
            overflow: hidden;
        }

        .welcome-header::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 200px;
            height: 200px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            transform: translate(50px, -50px);
        }

        .welcome-header h1 {
            margin: 0 0 15px 0;
            font-size: 32px;
            font-weight: 600;
            position: relative;
            z-index: 1;
        }

        .welcome-header p {
            margin: 0;
            opacity: 0.95;
            font-size: 18px;
            position: relative;
            z-index: 1;
        }

        .welcome-stats {
            display: flex;
            gap: 30px;
            margin-top: 25px;
            position: relative;
            z-index: 1;
        }

        .welcome-stat {
            text-align: center;
        }

        .welcome-stat-number {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .welcome-stat-label {
            font-size: 14px;
            opacity: 0.8;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .dashboard-card {
            background: white;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow-light);
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-medium);
        }

        .dashboard-card-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            padding: 20px 25px;
            font-size: 18px;
            font-weight: 600;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
        }

        .dashboard-card-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--accent-color), var(--primary-light));
        }

        .dashboard-card-header a {
            color: white;
            text-decoration: none;
            font-size: 14px;
            opacity: 0.9;
            padding: 5px 10px;
            border-radius: 15px;
            background: rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .dashboard-card-header a:hover {
            opacity: 1;
            background: rgba(255, 255, 255, 0.2);
        }

        .dashboard-card-body {
            padding: 25px;
        }

        /* Quick Actions Section */
        .quick-actions {
            margin-bottom: 40px;
        }

        .quick-actions h2 {
            font-size: 24px;
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 20px;
            text-align: center;
        }

        .action-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            max-width: 800px;
            margin: 0 auto;
        }

        .action-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 25px 20px;
            background: white;
            border-radius: var(--border-radius);
            text-decoration: none;
            color: var(--text-dark);
            box-shadow: var(--shadow-light);
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .action-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-color);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .action-btn:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-medium);
        }

        .action-btn:hover::before {
            transform: scaleX(1);
        }

        .action-btn.primary::before { background: var(--primary-color); }
        .action-btn.secondary::before { background: var(--success-color); }
        .action-btn.tertiary::before { background: var(--warning-color); }
        .action-btn.quaternary::before { background: var(--info-color); }

        .action-btn i {
            font-size: 32px;
            margin-bottom: 15px;
            color: var(--primary-color);
        }

        .action-btn.primary i { color: var(--primary-color); }
        .action-btn.secondary i { color: var(--success-color); }
        .action-btn.tertiary i { color: var(--warning-color); }
        .action-btn.quaternary i { color: var(--info-color); }

        .action-btn span {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .action-btn small {
            font-size: 12px;
            color: #666;
            text-align: center;
        }
        
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #666;
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.5;
        }
        
        .class-item {
            display: flex;
            padding: 15px 0;
            border-bottom: 1px solid #eee;
        }
        
        .class-item:last-child {
            border-bottom: none;
        }
        
        .class-time {
            min-width: 80px;
            text-align: center;
            margin-right: 15px;
        }
        
        .class-time div:first-child {
            font-weight: bold;
            color: var(--primary-color);
        }
        
        .class-time div:last-child {
            font-size: 12px;
            color: #666;
        }
        
        .class-details {
            flex: 1;
        }
        
        .class-subject {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .class-info {
            font-size: 14px;
            color: #666;
        }
        
        .class-info span {
            margin-right: 15px;
        }
        
        .notification-item {
            padding: 15px 0;
            border-bottom: 1px solid #eee;
        }
        
        .notification-item:last-child {
            border-bottom: none;
        }
        
        .notification-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .notification-time {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }
        
        .notification-content {
            font-size: 14px;
            color: #333;
        }
        
        .venue-item {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #eee;
        }
        
        .venue-item:last-child {
            border-bottom: none;
        }
        
        .venue-icon {
            width: 40px;
            height: 40px;
            background: var(--success-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: 15px;
        }
        
        .venue-name {
            margin: 0 0 5px 0;
            font-size: 16px;
        }
        
        .venue-location {
            margin: 0;
            font-size: 14px;
            color: #666;
        }
        
        .checkin-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #eee;
        }
        
        .checkin-item:last-child {
            border-bottom: none;
        }
        
        .checkin-details h4 {
            margin: 0 0 5px 0;
        }
        
        .checkin-details p {
            margin: 0;
            font-size: 14px;
            color: #666;
        }
        
        .checkin-time {
            text-align: right;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <?php include('CSS/studentsidebar.php'); ?>
    
    <div class="container">
        <div class="welcome-header">
            <h1>Welcome back, <?php echo htmlspecialchars($user_data['full_name']); ?>!</h1>
            <p><?php echo htmlspecialchars($user_data['course']); ?> - Year <?php echo $user_data['year']; ?> (<?php echo htmlspecialchars($user_data['stream']); ?>)</p>

            <?php
            // Get stats for welcome header
            $total_checkins_query = "SELECT COUNT(*) as total FROM venue_checkins WHERE user_id = ?";
            $stmt = mysqli_prepare($conn, $total_checkins_query);
            mysqli_stmt_bind_param($stmt, "i", $user_id);
            mysqli_stmt_execute($stmt);
            $total_checkins = mysqli_fetch_assoc(mysqli_stmt_get_result($stmt))['total'];

            $today_checkins_query = "SELECT COUNT(*) as today FROM venue_checkins WHERE user_id = ? AND DATE(check_in_time) = CURDATE()";
            $stmt = mysqli_prepare($conn, $today_checkins_query);
            mysqli_stmt_bind_param($stmt, "i", $user_id);
            mysqli_stmt_execute($stmt);
            $today_checkins = mysqli_fetch_assoc(mysqli_stmt_get_result($stmt))['today'];

            $classes_today_query = "SELECT COUNT(*) as classes FROM timetables WHERE course = ? AND year = ? AND stream = ? AND day = ?";
            $today = date('l');
            $stmt = mysqli_prepare($conn, $classes_today_query);
            mysqli_stmt_bind_param($stmt, "siss", $user_data['course'], $user_data['year'], $user_data['stream'], $today);
            mysqli_stmt_execute($stmt);
            $classes_today = mysqli_fetch_assoc(mysqli_stmt_get_result($stmt))['classes'];
            ?>

            <div class="welcome-stats">
                <div class="welcome-stat">
                    <div class="welcome-stat-number"><?php echo $total_checkins; ?></div>
                    <div class="welcome-stat-label">Total Check-ins</div>
                </div>
                <div class="welcome-stat">
                    <div class="welcome-stat-number"><?php echo $today_checkins; ?></div>
                    <div class="welcome-stat-label">Today's Check-ins</div>
                </div>
                <div class="welcome-stat">
                    <div class="welcome-stat-number"><?php echo $classes_today; ?></div>
                    <div class="welcome-stat-label">Classes Today</div>
                </div>
            </div>
        </div>

        <!-- Quick Actions Section -->
        <div class="quick-actions">
            <h2>Quick Actions</h2>
            <div class="action-buttons">
                <a href="scan_venue.php" class="action-btn primary">
                    <i class="fas fa-qrcode"></i>
                    <span>Scan QR Code</span>
                    <small>Mark attendance</small>
                </a>
                <a href="student_schedule.php" class="action-btn secondary">
                    <i class="fas fa-calendar-day"></i>
                    <span>View Schedule</span>
                    <small>Today's classes</small>
                </a>
                <a href="free_venues.php" class="action-btn tertiary">
                    <i class="fas fa-door-open"></i>
                    <span>Free Venues</span>
                    <small>Available now</small>
                </a>
                <a href="notifications.php" class="action-btn quaternary">
                    <i class="fas fa-bell"></i>
                    <span>Notifications</span>
                    <small>Latest updates</small>
                </a>
            </div>
        </div>

        <div class="dashboard-grid">
            <div class="dashboard-card">
                <div class="dashboard-card-header">
                    <span><i class="fas fa-history"></i> Recent Check-ins</span>
                    <a href="venue_history.php">View All</a>
                </div>
                <div class="dashboard-card-body">
                    <?php if (mysqli_num_rows($checkins_result) > 0): ?>
                        <?php while ($checkin = mysqli_fetch_assoc($checkins_result)): ?>
                            <div class="checkin-item">
                                <div class="checkin-details">
                                    <h4><?php echo htmlspecialchars($checkin['venuename']); ?></h4>
                                    <p><i class="fas fa-map-marker-alt"></i> <?php echo htmlspecialchars($checkin['location'] ?? 'Location not specified'); ?></p>
                                </div>
                                <div class="checkin-time">
                                    <div><?php echo date('M d', strtotime($checkin['check_in_time'])); ?></div>
                                    <div><?php echo date('h:i A', strtotime($checkin['check_in_time'])); ?></div>
                                </div>
                            </div>
                        <?php endwhile; ?>
                    <?php else: ?>
                        <div class="empty-state">
                            <i class="fas fa-history"></i>
                            <p>No recent check-ins</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="dashboard-grid">
            <div class="dashboard-card">
                <div class="dashboard-card-header">
                    <span><i class="fas fa-calendar-day"></i> Today's Classes</span>
                    <a href="student_schedule.php">View Schedule</a>
                </div>
                <div class="dashboard-card-body">
                    <?php
                    // Get today's classes
                    $today = date('l'); // Current day name (Monday, Tuesday, etc.)
                    $classes_query = "SELECT * FROM timetables
                                     WHERE course = ? AND year = ? AND stream = ? AND day = ?
                                     ORDER BY start_time ASC";
                    $stmt = mysqli_prepare($conn, $classes_query);
                    mysqli_stmt_bind_param($stmt, "siss", $user_data['course'], $user_data['year'], $user_data['stream'], $today);
                    mysqli_stmt_execute($stmt);
                    $classes_result = mysqli_stmt_get_result($stmt);
                    ?>

                    <?php if (mysqli_num_rows($classes_result) > 0): ?>
                        <?php while ($class = mysqli_fetch_assoc($classes_result)): ?>
                            <div class="class-item">
                                <div class="class-time">
                                    <div><?php echo date("h:i A", strtotime($class['start_time'])); ?></div>
                                    <div><?php echo date("h:i A", strtotime($class['end_time'])); ?></div>
                                </div>
                                <div class="class-details">
                                    <div class="class-subject"><?php echo htmlspecialchars($class['subject_code']); ?></div>
                                    <div class="class-info">
                                        <span><i class="fas fa-map-marker-alt"></i> <?php echo htmlspecialchars($class['venue']); ?></span>
                                        <span><i class="fas fa-user"></i> <?php echo htmlspecialchars($class['lecturer']); ?></span>
                                    </div>
                                </div>
                            </div>
                        <?php endwhile; ?>
                    <?php else: ?>
                        <div class="empty-state">
                            <i class="fas fa-calendar-times"></i>
                            <p>You have no classes scheduled for today.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <div class="dashboard-card">
                <div class="dashboard-card-header">
                    <span><i class="fas fa-bell"></i> Notifications</span>
                    <a href="notifications.php">View All</a>
                </div>
                <div class="dashboard-card-body">
                    <?php
                    // Get notifications
                    $notifications_query = "SELECT * FROM notifications
                                           WHERE user_id = ?
                                           ORDER BY created_at DESC LIMIT 5";
                    $stmt = mysqli_prepare($conn, $notifications_query);
                    mysqli_stmt_bind_param($stmt, "i", $user_id);
                    mysqli_stmt_execute($stmt);
                    $notifications_result = mysqli_stmt_get_result($stmt);
                    ?>

                    <?php if (mysqli_num_rows($notifications_result) > 0): ?>
                        <?php while ($notification = mysqli_fetch_assoc($notifications_result)): ?>
                            <div class="notification-item">
                                <div class="notification-title">
                                    <?php echo htmlspecialchars($notification['title']); ?>
                                </div>
                                <div class="notification-time">
                                    <?php echo date('M d, g:i A', strtotime($notification['created_at'])); ?>
                                </div>
                                <div class="notification-content">
                                    <?php echo htmlspecialchars($notification['message']); ?>
                                </div>
                            </div>
                        <?php endwhile; ?>
                    <?php else: ?>
                        <div class="empty-state">
                            <i class="fas fa-bell-slash"></i>
                            <p>No notifications at this time</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="dashboard-grid">
            <div class="dashboard-card">
                <div class="dashboard-card-header">
                    <span><i class="fas fa-building"></i> Available Venues</span>
                    <a href="venues.php">View All</a>
                </div>
                <div class="dashboard-card-body">
                    <?php if (mysqli_num_rows($venues_result) > 0): ?>
                        <?php while ($venue = mysqli_fetch_assoc($venues_result)): ?>
                            <div class="venue-item">
                                <div class="venue-icon">
                                    <i class="fas fa-check"></i>
                                </div>
                                <div class="venue-details">
                                    <h4 class="venue-name"><?php echo htmlspecialchars($venue['venuename']); ?></h4>
                                    <p class="venue-location">
                                        <i class="fas fa-map-marker-alt"></i>
                                        <?php echo htmlspecialchars($venue['location'] ?? $venue['venuedescription']); ?>
                                    </p>
                                </div>
                            </div>
                        <?php endwhile; ?>
                    <?php else: ?>
                        <div class="empty-state">
                            <i class="fas fa-info-circle"></i>
                            <p>No available venues found</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Add active class to current page link
        document.addEventListener('DOMContentLoaded', function() {
            const currentPage = window.location.pathname.split('/').pop();
            const menuLinks = document.querySelectorAll('#menu a');

            menuLinks.forEach(link => {
                const linkPage = link.getAttribute('href');
                if (linkPage === currentPage) {
                    link.classList.add('active');
                }
            });
        });
    </script>

</body>
</html>

<?php
session_start();
require_once "database.php";

// Redirect if not a logged-in student
if (!isset($_SESSION["user"]) || $_SESSION["role"] !== "student") {
    header("Location: login.php");
    exit();
}

// Default values from session
$course = isset($_SESSION['course']) ? $_SESSION['course'] : '';
$year = isset($_SESSION['year']) ? $_SESSION['year'] : '';
$stream = isset($_SESSION['stream']) ? $_SESSION['stream'] : '';

// If form is submitted, override the session values
if ($_SERVER["REQUEST_METHOD"] === "POST") {
    $course = $_POST['course'] ?? $course;
    $year = $_POST['year'] ?? $year;
    $stream = $_POST['stream'] ?? $stream;
}

// Always fetch timetable (either using session or form data)
$query = "SELECT * FROM timetables 
          WHERE course = ? AND year = ? AND stream = ?
          ORDER BY FIELD(day, 'Monday','Tuesday','Wednesday','Thursday','Friday','Saturday'), start_time";

$stmt = mysqli_prepare($conn, $query);
mysqli_stmt_bind_param($stmt, "sis", $course, $year, $stream);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);
?>


<!DOCTYPE html>
<html lang="en">
<head>
    <?php include('CSS/header.php'); ?>
    <title>Class Schedule | Student Dashboard</title>
    <style>
        :root {
            --primary-color: #175883;
            --primary-dark: #0d3c60;
            --primary-light: #2980b9;
            --accent-color: #cbb09c;
            --text-light: #ffffff;
            --text-dark: #333333;
            --bg-light: #f8f9fa;
            --bg-dark: #2d3a4b;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
        }
        
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            color: var(--text-dark);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            margin-left: 270px;
            transition: margin-left 0.3s ease;
        }

        @media (max-width: 768px) {
            .container {
                margin-left: 0;
                padding: 15px;
            }
        }
        
        .page-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
            color: white;
            padding: 40px;
            border-radius: 12px;
            margin-bottom: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.12);
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 200px;
            height: 200px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            transform: translate(50px, -50px);
        }

        .page-header h2 {
            margin: 0 0 15px 0;
            font-size: 32px;
            font-weight: 600;
            position: relative;
            z-index: 1;
        }

        .page-header p {
            margin: 0;
            opacity: 0.95;
            font-size: 18px;
            position: relative;
            z-index: 1;
        }
        
        .filter-card {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
            border: 1px solid #e9ecef;
            margin-bottom: 30px;
        }

        .filter-card h3 {
            margin: 0 0 20px 0;
            color: var(--primary-color);
            font-size: 18px;
            font-weight: 600;
        }

        form {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            align-items: end;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            color: var(--text-dark);
            font-weight: 500;
        }
        
        select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        button {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }
        
        button:hover {
            background: var(--primary-dark);
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
        }
        
        th, td {
            text-align: left;
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
        }
        
        th {
            background-color: var(--primary-color);
            color: white;
            font-weight: 500;
        }
        
        tr:last-child td {
            border-bottom: none;
        }
        
        tr:nth-child(even) {
            background-color: rgba(0,0,0,0.02);
        }
        
        tr:hover {
            background-color: rgba(23, 88, 131, 0.05);
        }
        
        p {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            color: var(--text-dark);
            text-align: center;
        }
        
        @media (max-width: 768px) {
            form {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 0 10px;
                margin: 20px auto;
            }
            
            table {
                display: block;
                overflow-x: auto;
            }
        }
    </style>
</head>
<body>

<?php include('CSS/studentsidebar.php'); ?>

<div class="container">
    <div class="page-header">
        <h2><i class="fas fa-calendar-alt"></i> Your Class Timetable</h2>
        <p>View your weekly class schedule and plan your day</p>
    </div>

    <div class="filter-card">
        <h3><i class="fas fa-filter"></i> Filter Schedule</h3>
        <form method="post">
        <div>
            <label>Course:</label>
            <select name="course" required>
                <option value="">-- Select Course --</option>
                <option value="BSc IT" <?= $course === 'BSc IT' ? 'selected' : '' ?>>BSc IT</option>
                <option value="ODIT" <?= $course === 'ODIT' ? 'selected' : '' ?>>ODIT</option>
            </select>
        </div>

        <div>
            <label>Year:</label>
            <select name="year" required>
                <option value="">-- Select Year --</option>
                <?php for ($i = 1; $i <= 4; $i++): ?>
                    <option value="<?= $i ?>" <?= $year == $i ? 'selected' : '' ?>><?= $i ?></option>
                <?php endfor; ?>
            </select>
        </div>

        <div>
            <label>Stream:</label>
            <select name="stream" required>
                <option value="">-- Select Stream --</option>
                <option value="A" <?= $stream === 'A' ? 'selected' : '' ?>>A</option>
                <option value="B" <?= $stream === 'B' ? 'selected' : '' ?>>B</option>
                <option value="C" <?= $stream === 'C' ? 'selected' : '' ?>>C</option>
                <option value="SysDev" <?= $stream === 'SysDev' ? 'selected' : '' ?>>SysDev</option>
                <option value="SysAdmin" <?= $stream === 'SysAdmin' ? 'selected' : '' ?>>SysAdmin</option>
            </select>
        </div>

            <div>
                <button type="submit"><i class="fas fa-search"></i> View Schedule</button>
            </div>
        </form>
    </div>

    <?php if ($result && mysqli_num_rows($result) > 0): ?>
        <table>
            <tr>
                <th>Day</th>
                <th>Start</th>
                <th>End</th>
                <th>Subject</th>
                <th>Type</th>
                <th>Venue</th>
                <th>Lecturer</th>
            </tr>
            <?php while ($row = mysqli_fetch_assoc($result)): ?>
                <tr>
                    <td><?= htmlspecialchars($row['day']) ?></td>
                    <td><?= htmlspecialchars($row['start_time']) ?></td>
                    <td><?= htmlspecialchars($row['end_time']) ?></td>
                    <td><?= htmlspecialchars($row['subject_code']) ?></td>
                    <td><?= htmlspecialchars($row['type']) ?></td>
                    <td><?= htmlspecialchars($row['venue']) ?></td>
                    <td><?= htmlspecialchars($row['lecturer']) ?></td>
                </tr>
            <?php endwhile; ?>
        </table>
    <?php else: ?>
        <p><i class="fas fa-info-circle"></i> No timetable found for the selected filters.</p>
    <?php endif; ?>
</div>

<script>
    // Add active class to current page link
    document.addEventListener('DOMContentLoaded', function() {
        const currentPage = window.location.pathname.split('/').pop();
        const menuLinks = document.querySelectorAll('#menu a');
        
        menuLinks.forEach(link => {
            const linkPage = link.getAttribute('href');
            if (linkPage === currentPage) {
                link.classList.add('active');
            }
        });
    });
</script>

</body>
</html>

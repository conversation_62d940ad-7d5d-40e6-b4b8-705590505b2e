<head>
	<title>IFM</title>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<!-- Compiled and minified CSS -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/css/bootstrap.min.css" integrity="sha384-Zenh87qX5JnK2Jl0vWa8Ck2rdkQ2Bzep5IDxbcnCeuOxjzrPF/et3URy9Bv1WTRi" crossorigin="anonymous">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
  <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    
  <style type="text/css">
    * {
      box-sizing: border-box;
    }
    
    body {
      margin: 0;
      padding: 0;
      font-family: 'Roboto', sans-serif;
    }

    :root {
      --primary-color: #175883;
      --primary-dark: #0d3c60;
      --primary-light: #2980b9;
      --accent-color: #cbb09c;
      --text-light: #ffffff;
      --text-dark: #333333;
      --bg-light: #f8f9fa;
      --bg-dark: #2d3a4b;
      --success-color: #2ecc71;
      --warning-color: #f39c12;
      --danger-color: #e74c3c;
      --info-color: #3498db;
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: 'Roboto', sans-serif;
      background-color: var(--bg-light);
      color: var(--text-dark);
      line-height: 1.6;
    }

    .brand {
      background: var(--primary-color) !important;
    }

    .brand-text {
      color: var(--primary-color) !important;
    }

    .accent-text {
      color: var(--accent-color) !important;
    }

    form {
      max-width: 460px;
      margin: 20px auto;
      padding: 25px;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
    }

    .container {
      padding: 20px;
      margin-top: 60px;
    }

    .card {
      border-radius: 8px;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .card:hover {
      transform: translateY(-5px);
      box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
    }

    .btn {
      border-radius: 4px;
      padding: 10px 20px;
      transition: all 0.3s ease;
    }

    .btn-primary {
      background-color: var(--primary-color);
      border-color: var(--primary-color);
    }

    .btn-primary:hover {
      background-color: var(--primary-dark);
      border-color: var(--primary-dark);
    }

    .btn-accent {
      background-color: var(--accent-color);
      border-color: var(--accent-color);
      color: white;
    }

    .btn-accent:hover {
      background-color: #b89d8c;
      border-color: #b89d8c;
    }

    /* Stats cards styling */
    .stat-card {
      background-color: white;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease;
    }

    .stat-card:hover {
      transform: translateY(-5px);
    }

    .stat-card h2 {
      font-size: 18px;
      color: var(--primary-color);
      margin-bottom: 10px;
    }

    .stat-card .count-number {
      font-size: 28px;
      font-weight: bold;
      color: var(--text-dark);
    }

    /* Table styling */
    table {
      width: 100%;
      border-collapse: collapse;
      margin: 20px 0;
      background-color: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    }

    table th {
      background-color: var(--primary-color);
      color: white;
      padding: 12px 15px;
      text-align: left;
    }

    table td {
      padding: 12px 15px;
      border-bottom: 1px solid #f2f2f2;
    }

    table tr:last-child td {
      border-bottom: none;
    }

    table tr:hover {
      background-color: #f9f9f9;
    }

    /* Header styling */
    .page-header {
      background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
      color: white;
      padding: 20px;
      border-radius: 8px;
      margin-bottom: 20px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    /* Form controls */
    input[type="text"],
    input[type="password"],
    input[type="email"],
    input[type="number"],
    textarea,
    select {
      width: 100%;
      padding: 10px 15px;
      margin-bottom: 15px;
      border: 1px solid #ddd;
      border-radius: 4px;
      transition: border-color 0.3s;
    }

    input[type="text"]:focus,
    input[type="password"]:focus,
    input[type="email"]:focus,
    input[type="number"]:focus,
    textarea:focus,
    select:focus {
      border-color: var(--primary-color);
      outline: none;
      box-shadow: 0 0 0 2px rgba(23, 88, 131, 0.2);
    }

    label {
      font-weight: 500;
      margin-bottom: 8px;
      display: block;
      color: var(--text-dark);
    }

    /* Custom header */
    .ifm-header {
      background-color: white;
      padding: 15px 0;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      z-index: 4;
    }

    .ifm-header h1 {
      margin: 0;
      color: var(--primary-color);
      font-size: 1.5rem;
      font-weight: 600;
      text-align: center;
    }
  </style>
</head>

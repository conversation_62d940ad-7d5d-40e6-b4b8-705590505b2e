<?php
session_start();

// Check if the user is logged in and is an admin
if (!isset($_SESSION["user"]) || $_SESSION["role"] !== "admin") {
    // Redirect unauthorized users to login page
    header("Location: login.php");
    exit();
}

// Database connection
require_once "database.php";

// Include the main QR Code library
require_once "qrlib.php";

// Include the Simple QR Code library
require_once "simple_qr.php";

// Include the QR generator utility
require_once "qrgenerator.php";

// Initialize variables
$venue = null;
$error_message = "";
$qr_image_path = "";

// Check if venue ID is provided
if (isset($_GET['id']) && !empty($_GET['id'])) {
    $venue_id = $_GET['id'];
    
    // Get venue details
    $sql = "SELECT * FROM venues WHERE venueid = ?";
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, "i", $venue_id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    if (mysqli_num_rows($result) === 1) {
        $venue = mysqli_fetch_assoc($result);
        
        // Create QR code data
        $qr_data = json_encode([
            'venueid' => $venue['venueid'],
            'venuename' => $venue['venuename'],
            'location' => $venue['location'],
            'capacity' => $venue['capacity']
        ]);

        // Generate QR code file name using venue name
        $qr_filename = 'venue_qr_' . $venue['venueid'] . '_' . time();

        // Set up QR code directory within the application (as fallback)
        $qr_dir = "qrcodes/";
        if (!is_dir($qr_dir)) {
            mkdir($qr_dir, 0755, true);
        }
        $qr_image_path = $qr_dir . $qr_filename . '.png';

        // Generate QR code with improved error handling
        try {
            // Check if directory is writable
            if (!is_writable($qr_dir)) {
                throw new Exception("QR codes directory is not writable. Please check permissions.");
            }

            // Try to save to desktop first using venue name as filename
            $desktop_path = generateQRCode($qr_data, $venue['venuename'], $venue['venueid']);

            // If successful, use the desktop path
            $qr_image_path = $desktop_path;
            $success_message = "QR code generated successfully and saved to desktop as '" . basename($desktop_path) . "'!";

            // Also save a copy in the web directory for display
            $web_success = QRcode::png($qr_data, $qr_dir . $qr_filename . '.png', QR_ECLEVEL_L, 10, 2);
            $web_path = $qr_dir . $qr_filename . '.png';

            if (!$web_success || !file_exists($web_path)) {
                throw new Exception("Failed to create web copy of QR code");
            }

            // Store path in database
            $update_sql = "UPDATE venues SET qrcode = ? WHERE venueid = ?";
            $update_stmt = mysqli_prepare($conn, $update_sql);
            mysqli_stmt_bind_param($update_stmt, "si", $web_path, $venue_id);
            mysqli_stmt_execute($update_stmt);

        } catch (Exception $e) {
            // If desktop save fails, save to web directory as fallback
            try {
                $web_success = QRcode::png($qr_data, $qr_image_path, QR_ECLEVEL_L, 10, 2);

                if ($web_success && file_exists($qr_image_path)) {
                    $success_message = "QR code generated successfully and saved to web directory!";
                    $error_message = "Note: Could not save to desktop - " . $e->getMessage() . ". QR code saved to web directory instead.";

                    // Store path in database
                    $update_sql = "UPDATE venues SET qrcode = ? WHERE venueid = ?";
                    $update_stmt = mysqli_prepare($conn, $update_sql);
                    mysqli_stmt_bind_param($update_stmt, "si", $qr_image_path, $venue_id);
                    mysqli_stmt_execute($update_stmt);
                } else {
                    $error_message = "Failed to create QR code file. Error: " . $e->getMessage() .
                                   ". Directory writable: " . (is_writable($qr_dir) ? "Yes" : "No") .
                                   ". Directory exists: " . (is_dir($qr_dir) ? "Yes" : "No");
                }
            } catch (Exception $inner_e) {
                $error_message = "Error generating QR code: " . $inner_e->getMessage() .
                               ". Original error: " . $e->getMessage();
            }
        }
    } else {
        $error_message = "Venue not found.";
    }
} else {
    $error_message = "Venue ID is required.";
}?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include('CSS/header.php'); ?>
    <title>Generate QR Code | Admin Dashboard</title>
    <style>
        :root {
            --primary-color: #175883;
            --primary-dark: #0d3c60;
            --primary-light: #2980b9;
            --accent-color: #cbb09c;
            --text-light: #ffffff;
            --text-dark: #333333;
            --bg-light: #f8f9fa;
            --bg-dark: #2d3a4b;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #3498db;
        }
        
        body {
            background-color: var(--bg-light);
            font-family: 'Roboto', sans-serif;
            margin: 0;
            padding: 0;
            color: var(--text-dark);
        }
        
        .container {
            padding: 30px;
            background-color: #f8f9fa;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0,0,0,0.05);
            margin: 20px;
            transition: margin-left 0.3s ease;
        }
        
        #menuToggle:checked ~ .container {
            margin-left: 270px;
        }
        
        .page-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            padding: 25px;
            border-radius: 8px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .page-header h1 {
            margin: 0 0 10px 0;
            font-size: 28px;
            font-weight: 500;
        }
        
        .page-header p {
            margin: 0;
            font-size: 16px;
            opacity: 0.9;
        }
        
        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 20px;
        }
        
        .card-header {
            background: var(--primary-light);
            color: white;
            padding: 15px 20px;
            font-size: 18px;
            font-weight: 500;
        }
        
        .card-body {
            padding: 20px;
        }
        
        .qr-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
        }
        
        .qr-image {
            max-width: 300px;
            margin-bottom: 20px;
            border: 1px solid #ddd;
            padding: 10px;
            background: white;
        }
        
        .venue-details {
            margin-bottom: 30px;
            width: 100%;
            max-width: 500px;
        }
        
        .venue-details h3 {
            margin-top: 0;
            color: var(--primary-color);
            border-bottom: 2px solid var(--primary-light);
            padding-bottom: 10px;
        }
        
        .venue-details p {
            margin: 10px 0;
            display: flex;
            justify-content: space-between;
        }
        
        .venue-details strong {
            color: var(--text-dark);
        }
        
        .btn {
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s;
            border: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            margin: 5px;
        }
        
        .btn i {
            margin-right: 5px;
        }
        
        .btn-primary {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .alert {
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .alert-danger {
            background-color: rgba(231, 76, 60, 0.2);
            border: 1px solid var(--danger-color);
            color: #c0392b;
        }

        .alert-success {
            background-color: rgba(46, 204, 113, 0.2);
            border: 1px solid var(--success-color);
            color: #27ae60;
        }
        
        .action-buttons {
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 15px;
                margin: 10px;
            }
            
            .page-header {
                padding: 20px;
            }
            
            .page-header h1 {
                font-size: 24px;
            }
            
            .qr-image {
                max-width: 200px;
            }
            
            #menuToggle:checked ~ .container {
                margin-left: 20px;
                opacity: 0.4;
            }
        }
    </style>
</head>
<body>

<?php include('CSS/sidebar.php'); ?>

<div class="container">
    <div class="page-header">
        <h1>Generate QR Code</h1>
        <p>Create a QR code for venue check-in and management.</p>
    </div>
    
    <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
        </div>
    <?php endif; ?>
    
    <?php if (!empty($success_message)): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
        </div>
    <?php endif; ?>
    
    <?php if ($venue): ?>
        <div class="card">
            <div class="card-header">
                <i class="fas fa-qrcode"></i> QR Code for <?php echo htmlspecialchars($venue['venuename']); ?>
            </div>
            <div class="card-body">
                <div class="qr-container">
                    <div class="venue-details">
                        <h3>Venue Information</h3>
                        <p><strong>Venue ID:</strong> <span><?php echo $venue['venueid']; ?></span></p>
                        <p><strong>Name:</strong> <span><?php echo htmlspecialchars($venue['venuename']); ?></span></p>
                        <p><strong>Location:</strong> <span><?php echo htmlspecialchars($venue['location']); ?></span></p>
                        <p><strong>Capacity:</strong> <span><?php echo $venue['capacity']; ?></span></p>
                        <p><strong>Status:</strong> <span><?php echo ucfirst($venue['status']); ?></span></p>
                    </div>
                    
                    <?php if (file_exists($qr_image_path)): ?>
                        <img src="<?php echo $qr_image_path; ?>" alt="QR Code" class="qr-image">
                        <p>QR code saved to: <?php echo $qr_image_path; ?></p>
                    <?php else: ?>
                        <p>QR code image could not be displayed. Please check the file location: <?php echo $qr_image_path; ?></p>
                    <?php endif; ?>
                    
                    <div class="action-buttons">
                        <a href="<?php echo $qr_image_path; ?>" download class="btn btn-primary">
                            <i class="fas fa-download"></i> Download QR Code
                        </a>
                        <a href="venues.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Venues
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <i class="fas fa-info-circle"></i> How to Use This QR Code
            </div>
            <div class="card-body">
                <ol>
                    <li>Download and print the QR code.</li>
                    <li>Place the printed QR code at the entrance of the venue.</li>
                    <li>Users can scan the QR code with the mobile app to check in and out of the venue.</li>
                    <li>The system will automatically track venue occupancy and usage.</li>
                </ol>
                <p><strong>Note:</strong> Each QR code is unique to the venue and contains encoded venue information.</p>
            </div>
        </div>
    <?php else: ?>
        <div class="card">
            <div class="card-body">
                <p>No venue selected or venue not found.</p>
                <a href="venues.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Venues
                </a>
            </div>
        </div>
    <?php endif; ?>
</div>

</body>
</html>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           